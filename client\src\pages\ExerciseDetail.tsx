import { useRoute } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { Exercise } from "@shared/schema";
import { ReadingExercise } from "@/components/skills/ReadingExercise";
import { WritingExercise } from "@/components/skills/WritingExercise";
import { ListeningExercise } from "@/components/skills/ListeningExercise";
import { SpeakingExercise } from "@/components/skills/SpeakingExercise";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useState } from "react";
import { FaMicrophone, FaMicrophoneSlash } from "react-icons/fa";
import { axiosInstance } from "@/lib/queryClient";

export default function ExerciseDetail() {
  const [, params] = useRoute("/:skill/:id");
  const skill = params?.skill;
  const id = params?.id;
  const [isRecording, setIsRecording] = useState(false);
  const { data: exercise, isLoading } = useQuery<Exercise>({
    queryKey: [`/api/question/1/?skill=${skill}&uid=test-user-123`],
    queryFn: async () => {
      const res = await axiosInstance.get(`/api/question/1?skill=${skill}&uid=test-user-123`);
      return res.data;
    },
  });

  if (isLoading) {
    return (
      <div className="mt-8">
        <Skeleton className="h-8 w-64 mb-4" />
        <Skeleton className="h-40 w-full" />
      </div>
    );
  }



  const renderExercise = () => {
    switch (skill) {
      case "reading":
        return <ReadingExercise exerciseId={Number(id)} />;
      case "writing":
        return <WritingExercise exerciseId={Number(id)} />;
      case "listening":
        return <ListeningExercise exercise={exercise} isLoading={isLoading} />;
      case "speaking":
        return <SpeakingExercise exerciseId={Number(id)} />;
      default:
        return <div>Invalid exercise type</div>;
    }
  };

  return (
    <div className="max-w-4xl mx-auto ">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{exercise?.title}</h1>
      </div>
      {renderExercise()}
      <Dialog>
        <TooltipProvider>
          <Tooltip>

            <TooltipTrigger asChild>
              <DialogTrigger asChild>
                <Button
                  size="lg"
                  className="absolute top-[75px] right-5 h-14 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all  group z-50"
                >
                  <div className="absolute flex inset-0 rounded-full bg-white/10 group-hover:bg-white/20 transition-all"></div>
                  <p className="text-2xl">🪄</p> Ask AI
                </Button>
              </DialogTrigger>
            </TooltipTrigger>

            <TooltipContent side="left">
              <p>AI Assistant</p>
            </TooltipContent>

          </Tooltip>
        </TooltipProvider>

        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>AI Assistant</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-2">
            <p>How can I help you with this exercise?</p>
            <div className="flex flex-col gap-4">
              <div className="relative w-full h-[300px] bg-gray-100 rounded-xl">
                {/* Video preview will be shown here */}
                <video
                  className="w-full h-full object-cover rounded-xl"
                  id="video-preview"
                />
              </div>

              <div className="flex items-center justify-center gap-4">
                <button
                  className="p-3 rounded-full text-white transition-colors border-2"
                  aria-label="Toggle microphone"
                >
                  {isRecording ? (
                    <FaMicrophone
                      size={24}
                      color="#2b14fa"
                      onClick={() => setIsRecording(false)}
                    />
                  ) : (
                    <FaMicrophoneSlash
                      size={24}
                      onClick={() => setIsRecording(true)}
                      color="gray"
                    />
                  )}
                </button>

                <button
                  className="px-4 py-2 rounded-md bg-red-500 hover:bg-red-600 text-white transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}