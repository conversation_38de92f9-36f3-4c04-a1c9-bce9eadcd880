import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { CheckCircle, XCircle, Volume2, Play, Pause, RotateCcw } from "lucide-react";
import { Skeleton } from "../ui/skeleton";

interface ListeningExerciseProps {
  exercise: any;
  isLoading: boolean;
}

export function ListeningExercise({ exercise, isLoading }: ListeningExerciseProps) {
  const [selectedAnswers, setSelectedAnswers] = useState<string[]>([]);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  
  // Audio player state
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [audioData, setAudioData] = useState<number[]>([]);
  const [isAudioLoaded, setIsAudioLoaded] = useState(false);
  
  const audioRef = useRef<HTMLAudioElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();

  // Convert base64 audio to blob and create audio URL
  useEffect(() => {
    if (exercise?.ex_audio) {
      try {
        // Decode base64 audio data
        const binaryString = atob(exercise.ex_audio);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        
        // Create blob and URL
        const audioBlob = new Blob([bytes], { type: 'audio/mpeg' });
        const audioUrl = URL.createObjectURL(audioBlob);
        
        if (audioRef.current) {
          audioRef.current.src = audioUrl;
          setIsAudioLoaded(true);
        }
        
        // Generate mock audio data for visualization
        const mockData = Array.from({ length: 100 }, () => Math.random() * 50 + 10);
        setAudioData(mockData);
        
      } catch (err) {
        console.error('Error processing audio data:', err);
      }
    }
  }, [exercise?.ex_audio]);

  // Audio event handlers
  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
    }
  };

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };

  const handleEnded = () => {
    setIsPlaying(false);
  };

  // Audio controls
  const togglePlayPause = () => {
    if (!audioRef.current) return;
    
    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      audioRef.current.play();
      setIsPlaying(true);
    }
  };

  const resetAudio = () => {
    if (!audioRef.current) return;
    audioRef.current.currentTime = 0;
    setCurrentTime(0);
    setIsPlaying(false);
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!audioRef.current) return;
    
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const newTime = (clickX / rect.width) * duration;
    
    audioRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  };

  // Canvas visualization
  useEffect(() => {
    const drawVisualization = () => {
      const canvas = canvasRef.current;
      if (!canvas || !audioData.length) return;
      
      const ctx = canvas.getContext('2d');
      if (!ctx) return;
      
      const { width, height } = canvas;
      ctx.clearRect(0, 0, width, height);
      
      const barWidth = width / audioData.length;
      const progress = duration > 0 ? currentTime / duration : 0;
      
      audioData.forEach((value, index) => {
        const barHeight = (value / 60) * height * 0.8;
        const x = index * barWidth;
        const y = (height - barHeight) / 2;
        
        // Color based on progress
        const isPlayed = index < progress * audioData.length;
        ctx.fillStyle = isPlayed ? '#3b82f6' : '#94a3b8';
        
        ctx.fillRect(x + 1, y, Math.max(barWidth - 2, 1), barHeight);
      });
    };

    drawVisualization();
    
    if (isPlaying) {
      animationRef.current = requestAnimationFrame(() => {
        drawVisualization();
      });
    }
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [audioData, currentTime, duration, isPlaying]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleAnswerSelect = (option: string) => {
    if (isSubmitted) return;
    
    setSelectedAnswers(prev => {
      if (prev.includes(option)) {
        return prev.filter(ans => ans !== option);
      } else {
        return [...prev, option];
      }
    });
  };

  const handleSubmit = () => {
    if (selectedAnswers.length === 0) return;
    
    setIsSubmitted(true);
    setShowResults(true);
    
    // Check if answers are correct
    const correctAnswers = Array.isArray(exercise.correct_ans) 
      ? exercise.correct_ans 
      : [exercise.correct_ans];
    
    const isAnswerCorrect = selectedAnswers.length === correctAnswers.length &&
      selectedAnswers.every(ans => correctAnswers.includes(ans));
    
    setIsCorrect(isAnswerCorrect);
  };

  const handleNext = () => {
    // Reset state for next exercise
    setSelectedAnswers([]);
    setIsSubmitted(false);
    setShowResults(false);
    setIsCorrect(false);
    resetAudio();
    console.log('Moving to next exercise...');
  };

  const getOptionStatus = (option: string) => {
    if (!showResults) return 'default';
    
    const correctAnswers = Array.isArray(exercise.correct_ans) 
      ? exercise.correct_ans 
      : [exercise.correct_ans];
    
    const isCorrectOption = correctAnswers.includes(option);
    const isSelected = selectedAnswers.includes(option);
    
    if (isSelected && isCorrectOption) return 'correct';
    if (isSelected && !isCorrectOption) return 'wrong';
    if (!isSelected && isCorrectOption) return 'missed';
    return 'default';
  };

  const getOptionIcon = (option: string) => {
    const status = getOptionStatus(option);
    if (status === 'correct') return <CheckCircle className="w-5 h-5 text-green-600" />;
    if (status === 'wrong') return <XCircle className="w-5 h-5 text-red-600" />;
    if (status === 'missed') return <CheckCircle className="w-5 h-5 text-green-600" />;
    return null;
  };

  const getOptionClassName = (option: string) => {
    const status = getOptionStatus(option);
    const baseClasses = "w-full p-4 text-left border-2 rounded-lg transition-all duration-200 flex items-center justify-between";
    
    if (!showResults) {
      const isSelected = selectedAnswers.includes(option);
      return `${baseClasses} ${
        isSelected 
          ? "border-blue-500 bg-blue-50 text-blue-700" 
          : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
      }`;
    }
    
    switch (status) {
      case 'correct':
        return `${baseClasses} border-green-500 bg-green-50 text-green-700`;
      case 'wrong':
        return `${baseClasses} border-red-500 bg-red-50 text-red-700`;
      case 'missed':
        return `${baseClasses} border-green-500 bg-green-100 text-green-700`;
      default:
        return `${baseClasses} border-gray-200 bg-gray-50 text-gray-600`;
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-6 bg-gray-200 rounded w-3/4"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="space-y-2">
              {[1, 2, 3, 4].map(i => (
                <Skeleton key={i} className="h-12 w-full rounded-lg" />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!exercise || Object.keys(exercise).length === 0) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
          <p className="text-gray-600">Exercise not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-sm border">
        {/* Header */}
        <div className="p-6 border-b bg-gradient-to-r from-blue-50 to-indigo-50">
          <div className="flex items-center gap-3 mb-4">
            <Volume2 className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-800">Listening Exercise</h2>
          </div>
          <p className="text-gray-700 text-lg">{exercise.q_text}</p>
        </div>

        <div className="p-6 border-b bg-gray-50">
          <div className="mb-4">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Listen to the audio:</h3>
          </div>
          <div className="bg-white rounded-lg border p-4">
            {/* Audio Element */}
            <audio
              ref={audioRef}
              onLoadedMetadata={handleLoadedMetadata}
              onTimeUpdate={handleTimeUpdate}
              onEnded={handleEnded}
              className="hidden"
            />
            
            {/* Visualization Canvas */}
            <div className="mb-4">
              <canvas
                ref={canvasRef}
                width={600}
                height={120}
                className="w-full h-30 bg-gray-50 rounded border"
                style={{ maxHeight: '120px' }}
              />
            </div>
            
            {/* Controls */}
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Button
                  onClick={togglePlayPause}
                  disabled={!isAudioLoaded}
                  size="sm"
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                  {isPlaying ? 'Pause' : 'Play'}
                </Button>
                
                <Button
                  onClick={resetAudio}
                  disabled={!isAudioLoaded}
                  size="sm"
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <RotateCcw className="w-4 h-4" />
                  Reset
                </Button>
              </div>
              
              {/* Progress Bar */}
              <div className="flex-1 flex items-center gap-2">
                <span className="text-sm text-gray-600 min-w-12">
                  {formatTime(currentTime)}
                </span>
                <div 
                  className="flex-1 h-2 bg-gray-200 rounded-full cursor-pointer"
                  onClick={handleProgressClick}
                >
                  <div 
                    className="h-full bg-blue-500 rounded-full transition-all duration-100"
                    style={{ width: duration > 0 ? `${(currentTime / duration) * 100}%` : '0%' }}
                  />
                </div>
                <span className="text-sm text-gray-600 min-w-12">
                  {formatTime(duration)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Answer Options */}
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-800 mb-4">
            Choose your answer{Array.isArray(exercise.correct_ans) ? 's' : ''}:
          </h3>
          <div className="space-y-3">
            {exercise.options.map((option, index) => (
              <button
                key={index}
                onClick={() => handleAnswerSelect(option)}
                disabled={isSubmitted}
                className={getOptionClassName(option)}
              >
                <span className="text-left flex-1">{option}</span>
                {getOptionIcon(option)}
              </button>
            ))}
          </div>
        </div>

        {/* Results Message */}
        {showResults && (
          <div className="px-6 pb-6">
            <div className={`p-4 rounded-lg border ${
              isCorrect 
                ? 'bg-green-50 border-green-200 text-green-800' 
                : 'bg-red-50 border-red-200 text-red-800'
            }`}>
              <div className="flex items-center gap-2">
                {isCorrect ? (
                  <CheckCircle className="w-5 h-5 text-green-600" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-600" />
                )}
                <span className="font-medium">
                  {isCorrect ? '🎉 Correct! Well done!' : '❌ Not quite right. Check the highlighted answers above.'}
                </span>
              </div>
            </div>
          </div>
        )}

        <div className="p-6 border-t bg-gray-50 flex justify-between">
          <div className="text-sm text-gray-600">
            Selected: {selectedAnswers.length} answer{selectedAnswers.length !== 1 ? 's' : ''}
          </div>
          <div className="flex gap-3">
            {!isSubmitted ? (
              <Button 
                onClick={handleSubmit}
                disabled={selectedAnswers.length === 0}
                className="px-6"
              >
                Submit Answer
              </Button>
            ) : (
              <Button 
                onClick={handleNext}
                disabled={!isCorrect}
                className={`px-6 ${isCorrect ? '' : 'opacity-50 cursor-not-allowed'}`}
              >
                Next Exercise →
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}